#!/usr/bin/env python3
"""
测试修复后的graph.py
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
from langchain_core.messages import HumanMessage

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 加载环境变量
load_dotenv()

def test_graph():
    """测试graph功能"""
    print("🧪 测试LangGraph集成...")
    
    try:
        from agent.graph import graph
        print("✅ Graph导入成功")
        
        # 创建测试状态
        test_state = {
            "messages": [HumanMessage(content="什么是人工智能？")],
            "initial_search_query_count": 1,  # 减少查询数量
            "max_research_loops": 1,  # 减少循环次数
        }
        
        print("🚀 开始测试graph.invoke...")
        result = graph.invoke(test_state)
        
        print("✅ Graph调用成功!")
        print(f"📝 响应消息数量: {len(result.get('messages', []))}")
        
        if result.get('messages'):
            last_message = result['messages'][-1]
            print(f"🤖 AI响应: {last_message.content[:200]}...")
        
        if result.get('sources_gathered'):
            print(f"📚 收集的源: {len(result['sources_gathered'])} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🔧 LangGraph 修复测试")
    print("=" * 60)
    
    success = test_graph()
    
    print("=" * 60)
    if success:
        print("🎉 测试通过！修复成功！")
    else:
        print("⚠️ 测试失败，需要进一步调试。")
