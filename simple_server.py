#!/usr/bin/env python3
"""
简单的FastAPI服务器 - 用于测试通义千问集成
不需要Docker，直接运行
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, List
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from dotenv import load_dotenv

# 添加src目录到Python路径
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

# 加载环境变量
load_dotenv()

# 检查API密钥
if not os.getenv("DASHSCOPE_API_KEY"):
    raise ValueError("DASHSCOPE_API_KEY is not set")

from langchain_community.chat_models import ChatTongyi
from langchain_core.messages import HumanMessage, AIMessage

app = FastAPI(title="通义千问 LangGraph API", version="1.0.0")

# 添加CORS支持
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class ChatRequest(BaseModel):
    message: str
    thread_id: str = "default"

class ChatResponse(BaseModel):
    response: str
    sources: List[Dict[str, Any]] = []

# 简单的内存存储（生产环境应该使用数据库）
conversations: Dict[str, List[Dict[str, Any]]] = {}

@app.get("/")
async def root():
    """根路径 - 返回API信息"""
    return {
        "message": "通义千问 LangGraph API 服务器",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "chat": "/chat",
            "health": "/health",
            "test": "/test"
        }
    }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 测试通义千问连接
        llm = ChatTongyi(
            model="qwen-turbo",
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
        )
        test_response = llm.invoke("Hello")
        
        return {
            "status": "healthy",
            "qwen_connection": "ok",
            "message": "服务器运行正常"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "message": "服务器连接异常"
        }

@app.post("/test")
async def test_qwen():
    """测试通义千问基本功能"""
    try:
        llm = ChatTongyi(
            model="qwen-turbo",
            dashscope_api_key=os.getenv("DASHSCOPE_API_KEY"),
        )
        
        response = llm.invoke("请用中文回答：你好，请介绍一下你自己。")
        
        return {
            "status": "success",
            "model": "qwen-turbo",
            "response": response.content,
            "message": "通义千问测试成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """聊天接口 - 使用LangGraph处理消息"""
    try:
        # 导入graph（延迟导入避免启动时错误）
        from agent.graph import graph
        
        # 获取或创建对话历史
        if request.thread_id not in conversations:
            conversations[request.thread_id] = []
        
        # 添加用户消息到历史
        conversations[request.thread_id].append({
            "role": "user",
            "content": request.message
        })
        
        # 构建消息历史
        messages = []
        for msg in conversations[request.thread_id]:
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            else:
                messages.append(AIMessage(content=msg["content"]))
        
        # 使用LangGraph处理
        state = {
            "messages": messages,
            "initial_search_query_count": 2,  # 减少查询数量以加快响应
            "max_research_loops": 1,  # 减少循环次数
        }
        
        result = graph.invoke(state)
        
        # 获取AI响应
        ai_response = result["messages"][-1].content if result.get("messages") else "抱歉，我无法处理您的请求。"
        sources = result.get("sources_gathered", [])
        
        # 添加AI响应到历史
        conversations[request.thread_id].append({
            "role": "assistant",
            "content": ai_response
        })
        
        return ChatResponse(
            response=ai_response,
            sources=sources
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理消息时出错: {str(e)}")

@app.get("/conversations/{thread_id}")
async def get_conversation(thread_id: str):
    """获取对话历史"""
    return {
        "thread_id": thread_id,
        "messages": conversations.get(thread_id, [])
    }

@app.delete("/conversations/{thread_id}")
async def clear_conversation(thread_id: str):
    """清除对话历史"""
    if thread_id in conversations:
        del conversations[thread_id]
    return {"message": f"对话 {thread_id} 已清除"}

if __name__ == "__main__":
    import uvicorn
    
    print("🚀 启动通义千问 LangGraph API 服务器...")
    print("📋 服务器信息:")
    print("- 地址: http://localhost:8123")
    print("- API文档: http://localhost:8123/docs")
    print("- 健康检查: http://localhost:8123/health")
    print("- 测试接口: http://localhost:8123/test")
    print("\n🔗 前端React项目应该配置API地址为: http://localhost:8123")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8123,
        log_level="info"
    )
